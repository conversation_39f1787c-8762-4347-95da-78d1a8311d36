import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { web3Profile } from '@/db/schema';
import { eq } from 'drizzle-orm';

export async function POST(request: NextRequest): Promise<Response> {
  try {
    const { address, transactionHash } = await request.json();

    if (!address || !transactionHash) {
      return Response.json(
        { error: 'Address and transaction hash are required' },
        { status: 400 }
      );
    }

    // Check if profile exists
    const profile = await db
      .select()
      .from(web3Profile)
      .where(eq(web3Profile.address, address));

    if (profile.length === 0) {
      return Response.json(
        { error: 'Profile not found' },
        { status: 404 }
      );
    }

    // Update profile with transaction hash and set status to pending
    await db.update(web3Profile)
      .set({
        transactionHash,
        status: 'pending',
        updatedAt: new Date()
      })
      .where(eq(web3Profile.address, address));

    return Response.json({ success: true });
  } catch (error) {
    console.error('Failed to submit transaction hash:', error);
    return Response.json(
      { error: 'Failed to submit transaction hash' },
      { status: 500 }
    );
  }
}
