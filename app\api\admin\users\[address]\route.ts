import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { web3Profile } from '@/db/schema';
import { eq } from 'drizzle-orm';

type Context = {
  params: Promise<{
    address: string;
  }>;
};

// GET a specific user by address
export async function GET(
  _request: NextRequest,
  context: Context
): Promise<Response> {
  try {
    const { address } = await context.params;

    if (!address) {
      return Response.json(
        { error: 'Address is required' },
        { status: 400 }
      );
    }

    // Get user by address
    const user = await db
      .select()
      .from(web3Profile)
      .where(eq(web3Profile.address, address));

    if (user.length === 0) {
      return Response.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return Response.json(user[0]);
  } catch (error) {
    console.error('Failed to fetch user:', error);
    return Response.json(
      { error: 'Failed to fetch user' },
      { status: 500 }
    );
  }
}

// PATCH to update a user's role or status
export async function PATCH(
  request: NextRequest,
  context: Context
): Promise<Response> {
  try {
    const { address } = await context.params;
    const data = await request.json();
    const { role, status, expiryDate, transactionHash } = data;

    if (!address) {
      return Response.json(
        { error: 'Address is required' },
        { status: 400 }
      );
    }

    // Check if user exists
    const existingUser = await db
      .select()
      .from(web3Profile)
      .where(eq(web3Profile.address, address));

    if (existingUser.length === 0) {
      return Response.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {
      updatedAt: new Date(),
    };

    if (role !== undefined) updateData.role = role;
    if (status !== undefined) updateData.status = status;
    if (expiryDate !== undefined) updateData.expiryDate = expiryDate ? new Date(expiryDate) : null;
    if (transactionHash !== undefined) updateData.transactionHash = transactionHash;

    // Update user
    await db.update(web3Profile)
      .set(updateData)
      .where(eq(web3Profile.address, address));

    // Get updated user
    const updatedUser = await db
      .select()
      .from(web3Profile)
      .where(eq(web3Profile.address, address));

    return Response.json(updatedUser[0]);
  } catch (error) {
    console.error('Failed to update user:', error);
    return Response.json(
      { error: 'Failed to update user' },
      { status: 500 }
    );
  }
}
